import { Request, Response, NextFunction } from 'express';
import { generateRequestId, logger } from '@/utils/logger';

// Extend Request interface to include requestId
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
    }
  }
}

// Request logging middleware
export const requestLoggingMiddleware = (req: Request, _res: Response, next: NextFunction): void => {
  req.requestId = generateRequestId();
  logger.logRequest(req.requestId, req.originalUrl, req.body, req.method, req.ip || 'unknown');
  next();
};
