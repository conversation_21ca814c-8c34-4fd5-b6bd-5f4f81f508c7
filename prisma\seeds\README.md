# Location Data Seeding

This directory contains comprehensive seeding scripts for populating your database with countries, states, and cities data.

## 📁 File Structure

```
prisma/seeds/
├── index.ts              # Main orchestrator script
├── seed-utils.ts         # Shared utilities and transformations
├── countries-seed.ts     # Countries seeding logic
├── states-seed.ts        # States seeding logic
├── cities-seed.ts        # Cities seeding logic
└── README.md            # This file
```

## 🚀 Quick Start

### Seed All Location Data
```bash
npm run seed
```

### Seed Individual Components
```bash
# Seed only countries
npm run seed:countries

# Seed only states (requires countries to be seeded first)
npm run seed:states

# Seed only cities (requires states to be seeded first)
npm run seed:cities
```

### Advanced Options
```bash
# Reset all location data and re-seed
npm run seed -- --reset

# Check current seeding statistics
npm run seed -- --stats
```

## 📊 Data Sources

The seeding scripts read from JSON files located in `prisma/data/`:
- `countries.json` - Contains ~250 countries with full metadata
- `states.json` - Contains ~5,000 states/provinces worldwide
- `cities.json` - Contains ~35,000+ cities worldwide

## 🔄 Data Transformation

### Countries
- Maps ISO codes (2-letter, 3-letter, numeric)
- Handles timezone data as JSON objects
- Includes currency information and coordinates
- Generates missing ISO codes automatically

### States
- Links states to countries via country codes
- Handles missing country mappings gracefully
- Includes coordinates and state codes
- Supports multiple state code formats

### Cities
- Links cities to states via state codes
- Handles missing state mappings with fallbacks
- Includes coordinates for mapping applications
- Processes large datasets efficiently in batches

## ⚙️ Configuration

### Environment Variables
```env
SYSTEM_USER_ID=1  # User ID for audit fields (createdBy, updatedBy)
```

### Batch Processing
- Countries: 50 per batch
- States: 100 per batch  
- Cities: 200 per batch

## 🛡️ Error Handling

The seeding scripts include comprehensive error handling:
- **Graceful Failures**: Individual record failures don't stop the entire process
- **Transaction Safety**: Each batch is wrapped in database transactions
- **Detailed Logging**: Progress tracking with timestamps and statistics
- **Validation**: Data validation before insertion
- **Duplicate Prevention**: Checks for existing data before seeding

## 📈 Performance Features

- **Batch Processing**: Large datasets are processed in optimized batches
- **Transaction Management**: Efficient database transaction handling
- **Memory Optimization**: Streaming approach for large JSON files
- **Progress Tracking**: Real-time progress updates during seeding

## 🔍 Monitoring & Debugging

### Progress Logging
All operations include detailed logging:
```
[2024-01-15T10:30:00.000Z] 🌍 Starting countries seeding...
[2024-01-15T10:30:01.000Z] 📖 Reading countries data...
[2024-01-15T10:30:01.500Z] 📊 Found 250 countries to process
[2024-01-15T10:30:02.000Z] 💾 Inserting countries in batches of 50...
[2024-01-15T10:30:02.500Z] Inserting countries - Batch 1/5 (1/5)
```

### Error Reporting
Failed records are logged with detailed error information:
```
⚠️  Skipping state California: Country not found for code XX
Error inserting city New York: Duplicate entry 'New York-1' for key 'cities.name_stateId_unique'
```

## 🧪 Testing

### Verify Seeding Results
```bash
# Check seeding statistics
npm run seed -- --stats

# Expected output:
# 📊 Current seeding statistics:
#    • Countries: 250
#    • States: 4,500+
#    • Cities: 30,000+
#    • Is Seeded: Yes
```

### Database Verification
```sql
-- Check data integrity
SELECT 
  (SELECT COUNT(*) FROM countries) as countries,
  (SELECT COUNT(*) FROM states) as states,
  (SELECT COUNT(*) FROM cities) as cities;

-- Check relationships
SELECT c.name as country, COUNT(s.stateId) as states_count
FROM countries c
LEFT JOIN states s ON c.countryId = s.countryId
GROUP BY c.countryId, c.name
ORDER BY states_count DESC
LIMIT 10;
```

## 🔧 Customization

### Adding Custom Data
1. Add your data to the respective JSON files in `prisma/data/`
2. Ensure data follows the expected format
3. Run the seeding scripts

### Modifying Transformations
Edit the transformation functions in `seed-utils.ts`:
- `transformCountryData()`
- `transformStateData()`
- `transformCityData()`

### Batch Size Optimization
Adjust batch sizes in individual seed files based on your system performance:
```typescript
const BATCH_SIZE = 100; // Increase for better performance, decrease for memory constraints
```

## 🚨 Troubleshooting

### Common Issues

1. **"No countries found" Error**
   - Ensure countries are seeded before states
   - Run: `npm run seed:countries`

2. **"No states found" Error**
   - Ensure states are seeded before cities
   - Run: `npm run seed:states`

3. **Memory Issues with Large Datasets**
   - Reduce batch sizes in seed files
   - Increase Node.js memory limit: `node --max-old-space-size=4096`

4. **Database Connection Issues**
   - Verify DATABASE_URL in .env file
   - Ensure MySQL server is running
   - Check database permissions

### Reset and Re-seed
```bash
# Complete reset and re-seed
npm run seed -- --reset
```

## 📝 Notes

- Seeding is idempotent - running multiple times won't create duplicates
- The process respects foreign key constraints and seeds in correct order
- All timestamps and audit fields are automatically populated
- Data includes comprehensive timezone and coordinate information for mapping applications
