// Simple Logger Utility
// A lightweight logging solution for API requests, responses, and general logging

// Generate unique request ID
export const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Get current timestamp
const getTimestamp = (): string => {
  return new Date().toISOString();
};

// Simple Logger Class
class SimpleLogger {
  
  // Log API Request
  logRequest(requestId: string, url: string, data: any, method: string, ip: string): void {
    const timestamp = getTimestamp();
    const logMessage = `[${timestamp}] REQUEST [${requestId}] ${method} ${url} | IP: ${ip}`;
    
    console.log('\x1b[36m%s\x1b[0m', logMessage); // Cyan color
    
    if (data && Object.keys(data).length > 0) {
      console.log('\x1b[36m%s\x1b[0m', `[${timestamp}] REQUEST_DATA [${requestId}]:`, JSON.stringify(data, null, 2));
    }
  }

  // Log Successful Response
  logResponseSuccess(requestId: string, data: any, statusCode: number): void {
    const timestamp = getTimestamp();
    const logMessage = `[${timestamp}] RESPONSE_SUCCESS [${requestId}] Status: ${statusCode}`;
    
    console.log('\x1b[32m%s\x1b[0m', logMessage); // Green color
    
    if (data) {
      console.log('\x1b[32m%s\x1b[0m', `[${timestamp}] RESPONSE_DATA [${requestId}]:`, JSON.stringify(data, null, 2));
    }
  }

  // Log Error Response
  logResponseError(requestId: string, errorMessage: string, stack?: string): void {
    const timestamp = getTimestamp();
    const logMessage = `[${timestamp}] RESPONSE_ERROR [${requestId}] Error: ${errorMessage}`;
    
    console.log('\x1b[31m%s\x1b[0m', logMessage); // Red color
    
    if (stack) {
      console.log('\x1b[31m%s\x1b[0m', `[${timestamp}] ERROR_STACK [${requestId}]:\n${stack}`);
    }
  }

  // Simple Info Logger
  info(message: string, data?: any): void {
    const timestamp = getTimestamp();
    console.log('\x1b[34m%s\x1b[0m', `[${timestamp}] INFO: ${message}`); // Blue color
    
    if (data) {
      console.log('\x1b[34m%s\x1b[0m', `[${timestamp}] INFO_DATA:`, JSON.stringify(data, null, 2));
    }
  }

  // Simple Error Logger
  error(message: string, error?: Error | string): void {
    const timestamp = getTimestamp();
    console.log('\x1b[31m%s\x1b[0m', `[${timestamp}] ERROR: ${message}`); // Red color
    
    if (error) {
      if (error instanceof Error) {
        console.log('\x1b[31m%s\x1b[0m', `[${timestamp}] ERROR_DETAILS:`, error.message);
        if (error.stack) {
          console.log('\x1b[31m%s\x1b[0m', `[${timestamp}] ERROR_STACK:\n${error.stack}`);
        }
      } else {
        console.log('\x1b[31m%s\x1b[0m', `[${timestamp}] ERROR_DETAILS:`, error);
      }
    }
  }

  // Simple Warning Logger
  warning(message: string, data?: any): void {
    const timestamp = getTimestamp();
    console.log('\x1b[33m%s\x1b[0m', `[${timestamp}] WARNING: ${message}`); // Yellow color
    
    if (data) {
      console.log('\x1b[33m%s\x1b[0m', `[${timestamp}] WARNING_DATA:`, JSON.stringify(data, null, 2));
    }
  }
}

// Export singleton instance
export const logger = new SimpleLogger();
export default logger;
