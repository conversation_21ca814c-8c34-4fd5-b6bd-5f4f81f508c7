import { Request, Response, NextFunction } from 'express';
import { ResponseUtil } from '@/utils/response-util';

// Extend Request interface to include requestId
interface LoggedRequest extends Request {
  requestId?: string;
}

// 404 handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  
  const requestId = (req as LoggedRequest).requestId;
  ResponseUtil.error(res, 'Not Found', 404, undefined, requestId);
  
};

// Global error handling middleware
export const globalErrorHandler = (
  err: any,
  req: LoggedRequest,
  res: Response,
  next: NextFunction
): void => {
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Something went wrong!';
  const requestId = req.requestId;

  ResponseUtil.error(res, message, statusCode, err?.stack, requestId);
};
