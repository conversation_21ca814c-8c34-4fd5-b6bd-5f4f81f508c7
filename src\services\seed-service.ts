import { PrismaClient } from '@prisma/client';
import seedModule from '../../prisma/seeds/index';

const prisma = new PrismaClient();

/**
 * Service for managing location data seeding operations
 */
export class SeedService {
  
  /**
   * Seed all location data (countries, states, cities)
   */
  static async seedAllLocationData(): Promise<{
    success: boolean;
    message: string;
    stats?: {
      countries: number;
      states: number;
      cities: number;
    };
    error?: string;
  }> {
    try {
      await seedModule.seedLocationData();
      
      const stats = await seedModule.getSeedingStats();
      
      return {
        success: true,
        message: 'Location data seeded successfully',
        stats: {
          countries: stats.countries,
          states: stats.states,
          cities: stats.cities
        }
      };
    } catch (error) {
      console.error('Error seeding location data:', error);
      return {
        success: false,
        message: 'Failed to seed location data',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Seed only countries
   */
  static async seedCountries(): Promise<{
    success: boolean;
    message: string;
    count?: number;
    error?: string;
  }> {
    try {
      await seedModule.seedCountries();
      
      const count = await prisma.countries.count();
      
      return {
        success: true,
        message: 'Countries seeded successfully',
        count
      };
    } catch (error) {
      console.error('Error seeding countries:', error);
      return {
        success: false,
        message: 'Failed to seed countries',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Seed only states
   */
  static async seedStates(): Promise<{
    success: boolean;
    message: string;
    count?: number;
    error?: string;
  }> {
    try {
      await seedModule.seedStates();
      
      const count = await prisma.states.count();
      
      return {
        success: true,
        message: 'States seeded successfully',
        count
      };
    } catch (error) {
      console.error('Error seeding states:', error);
      return {
        success: false,
        message: 'Failed to seed states',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Seed only cities
   */
  static async seedCities(): Promise<{
    success: boolean;
    message: string;
    count?: number;
    error?: string;
  }> {
    try {
      await seedModule.seedCities();
      
      const count = await prisma.cities.count();
      
      return {
        success: true,
        message: 'Cities seeded successfully',
        count
      };
    } catch (error) {
      console.error('Error seeding cities:', error);
      return {
        success: false,
        message: 'Failed to seed cities',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get current seeding statistics
   */
  static async getSeedingStats(): Promise<{
    countries: number;
    states: number;
    cities: number;
    isSeeded: boolean;
  }> {
    try {
      return await seedModule.getSeedingStats();
    } catch (error) {
      console.error('Error getting seeding stats:', error);
      return {
        countries: 0,
        states: 0,
        cities: 0,
        isSeeded: false
      };
    }
  }

  /**
   * Reset all location data
   */
  static async resetLocationData(): Promise<{
    success: boolean;
    message: string;
    error?: string;
  }> {
    try {
      await seedModule.resetLocationData();
      
      return {
        success: true,
        message: 'Location data reset successfully'
      };
    } catch (error) {
      console.error('Error resetting location data:', error);
      return {
        success: false,
        message: 'Failed to reset location data',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Check if location data is already seeded
   */
  static async isLocationDataSeeded(): Promise<boolean> {
    try {
      const stats = await this.getSeedingStats();
      return stats.isSeeded;
    } catch (error) {
      console.error('Error checking if location data is seeded:', error);
      return false;
    }
  }

  /**
   * Seed location data if not already seeded
   */
  static async seedIfNotExists(): Promise<{
    success: boolean;
    message: string;
    wasAlreadySeeded: boolean;
    stats?: {
      countries: number;
      states: number;
      cities: number;
    };
    error?: string;
  }> {
    try {
      const isSeeded = await this.isLocationDataSeeded();
      
      if (isSeeded) {
        const stats = await this.getSeedingStats();
        return {
          success: true,
          message: 'Location data already exists',
          wasAlreadySeeded: true,
          stats: {
            countries: stats.countries,
            states: stats.states,
            cities: stats.cities
          }
        };
      }

      const result = await this.seedAllLocationData();
      
      return {
        success: result.success,
        message: result.message,
        wasAlreadySeeded: false,
        stats: result.stats,
        error: result.error
      };
    } catch (error) {
      console.error('Error in seedIfNotExists:', error);
      return {
        success: false,
        message: 'Failed to check or seed location data',
        wasAlreadySeeded: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

export default SeedService;
