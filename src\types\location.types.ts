// Timezone interface for country timezones JSON field
export interface Timezone {
  zoneName: string;           // e.g., "Asia/Kolkata"
  gmtOffset: number;          // GMT offset in seconds, e.g., 19800 for UTC+05:30
  gmtOffsetName: string;      // e.g., "UTC+05:30"
  abbreviation: string;       // e.g., "IST"
  tzName: string;            // e.g., "Indian Standard Time"
}

// Translation interface for country name translations
export interface CountryTranslations {
  [languageCode: string]: string; // e.g., { "hi": "भारत", "fr": "Inde", "es": "India" }
}

// Complete country data interface
export interface CountryData {
  countryId: number;
  isoCode: string;           // ISO 3166-1 alpha-2 (e.g., "IN")
  name: string;              // Country name (e.g., "India")
  phonecode: string;         // Phone code without + (e.g., "91")
  flag?: string;             // Country flag emoji (e.g., "🇮🇳")
  currency?: string;         // Currency code (e.g., "INR")
  latitude?: number;         // Latitude coordinate
  longitude?: number;        // Longitude coordinate
  timezones?: Timezone[];    // Array of timezone objects
}

// State data interface
export interface StateData {
  stateId: number;
  name: string;            // State name (e.g., "Telangana")
  isoCode: string;         // State/Province code (e.g., "TG")
  countryId: number;       // Country ID
  latitude?: number;       // Latitude coordinate
  longitude?: number;      // Longitude coordinate
}

// City data interface
export interface CityData {
  cityId: number;
  name: string;            // City name (e.g., "New Delhi")
  stateId: number;         // State ID
  latitude?: number;       // Latitude coordinate
  longitude?: number;      // Longitude coordinate
}

// Location hierarchy interface for API responses
export interface LocationHierarchy {
  country: CountryData;
  state?: StateData;
  city?: CityData;
}

// Search result interface
export interface LocationSearchResult {
  type: 'country' | 'state' | 'city';
  id: number;
  name: string;
  hierarchy: LocationHierarchy;
}
