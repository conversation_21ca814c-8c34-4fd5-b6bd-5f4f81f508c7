import { 
  prisma, 
  readJsonFile, 
  logProgress, 
  processBatch, 
  transformStateData,
  createCountryCodeMapping,
  StateRawData 
} from './seed-utils';

/**
 * Seed states data
 */
export const seedStates = async (): Promise<void> => {
  try {
    logProgress('🏛️ Starting states seeding...');
    
    // Check if states already exist
    const existingCount = await prisma.states.count();
    if (existingCount > 0) {
      logProgress(`⚠️  Found ${existingCount} existing states. Skipping states seeding.`);
      return;
    }

    // Ensure countries exist
    const countryCount = await prisma.countries.count();
    if (countryCount === 0) {
      throw new Error('No countries found. Please seed countries first.');
    }
    
    // Create country mapping
    logProgress('🗺️  Creating country code mapping...');
    const countryMapping = await createCountryCodeMapping();
    logProgress(`📊 Created mapping for ${countryMapping.size} countries`);
    
    // Read states data
    logProgress('📖 Reading states data...');
    const rawStates = readJsonFile<StateRawData>('states.json');
    logProgress(`📊 Found ${rawStates.length} states to process`);
    
    // Transform and validate data
    logProgress('🔄 Transforming states data...');
    const transformedStates: any[] = [];
    let skippedStates = 0;
    
    for (const state of rawStates) {
      try {
        // Find country ID
        let countryId: number | undefined;
        
        if (state.countryCode) {
          countryId = countryMapping.get(state.countryCode);
        }
        
        if (!countryId) {
          // Try to find by partial matching or common country codes
          const fallbackCountryId = findCountryIdFallback(state, countryMapping);
          if (fallbackCountryId) {
            countryId = fallbackCountryId;
          }
        }
        
        if (!countryId) {
          console.warn(`⚠️  Skipping state ${state.name}: Country not found for code ${state.countryCode}`);
          skippedStates++;
          continue;
        }
        
        const transformedState = transformStateData(state, countryId);
        transformedStates.push(transformedState);
        
      } catch (error) {
        console.error(`Error transforming state ${state.name}:`, error);
        skippedStates++;
        continue;
      }
    }
    
    logProgress(`📊 Transformed ${transformedStates.length} states, skipped ${skippedStates} states`);
    
    if (transformedStates.length === 0) {
      logProgress('⚠️  No valid states to insert');
      return;
    }
    
    // Batch insert states
    const BATCH_SIZE = 100;
    logProgress(`💾 Inserting states in batches of ${BATCH_SIZE}...`);
    
    await processBatch(
      transformedStates,
      BATCH_SIZE,
      async (batch) => {
        return await prisma.$transaction(async (tx) => {
          const results = [];
          for (const state of batch) {
            try {
              const result = await tx.states.create({
                data: state
              });
              results.push(result);
            } catch (error) {
              console.error(`Error inserting state ${state.name}:`, error);
              // Continue with other states in the batch
              continue;
            }
          }
          return results;
        });
      },
      'Inserting states'
    );
    
    // Verify insertion
    const finalCount = await prisma.states.count();
    logProgress(`✅ States seeding completed! Inserted ${finalCount} states.`);
    
  } catch (error) {
    console.error('❌ Error seeding states:', error);
    throw error;
  }
};

/**
 * Fallback function to find country ID when direct mapping fails
 */
const findCountryIdFallback = (state: StateRawData, countryMapping: Map<string, number>): number | undefined => {
  if (!state.countryCode) return undefined;
  
  // Common country code mappings
  const commonMappings: { [key: string]: string } = {
    'AF': 'AF', // Afghanistan
    'AL': 'AL', // Albania
    'DZ': 'DZ', // Algeria
    'US': 'US', // United States
    'IN': 'IN', // India
    'GB': 'GB', // United Kingdom
    'CA': 'CA', // Canada
    'AU': 'AU', // Australia
    'DE': 'DE', // Germany
    'FR': 'FR', // France
    'IT': 'IT', // Italy
    'ES': 'ES', // Spain
    'BR': 'BR', // Brazil
    'MX': 'MX', // Mexico
    'CN': 'CN', // China
    'JP': 'JP', // Japan
    'RU': 'RU', // Russia
    'ZA': 'ZA', // South Africa
    'EG': 'EG', // Egypt
    'NG': 'NG', // Nigeria
  };
  
  const mappedCode = commonMappings[state.countryCode];
  if (mappedCode) {
    return countryMapping.get(mappedCode);
  }
  
  return undefined;
};

/**
 * Main function to run states seeding independently
 */
const main = async () => {
  try {
    await seedStates();
  } catch (error) {
    console.error('Failed to seed states:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
};

// Run if this file is executed directly
if (require.main === module) {
  main();
}
