import { db } from '@/config/db-config';
import { Prisma } from '@prisma/client';
import { CountryData, StateData, CityData, LocationSearchResult } from '@/types/location.types';

/**
 * Get all countries with optional filtering and pagination
 */
export const getAllCountries = async (options?: {
  search?: string;
  region?: string;
  limit?: number;
  offset?: number;
}) => {
  const { search, region, limit = 50, offset = 0 } = options || {};

  const whereClause: Prisma.CountryWhereInput = {};

  if (search) {
    whereClause.OR = [
      { countryName: { contains: search, mode: 'insensitive' } },
      { isoCode: { contains: search, mode: 'insensitive' } },
      { iso3Code: { contains: search, mode: 'insensitive' } }
    ];
  }

  if (region) {
    whereClause.region = { equals: region, mode: 'insensitive' };
  }

  const [countries, total] = await Promise.all([
    db.country.findMany({
      where: whereClause,
      take: limit,
      skip: offset,
      orderBy: { countryName: 'asc' }
    }),
    db.country.count({ where: whereClause })
  ]);

  return {
    countries,
    pagination: {
      total,
      limit,
      offset,
      hasMore: offset + limit < total
    }
  };
};

/**
 * Get country by ID with full details
 */
export const getCountryById = async (countryId: number) => {
  const country = await db.country.findUnique({
    where: { countryId },
    include: {
      states: {
        orderBy: { stateName: 'asc' },
        take: 10 // Limit states for performance
      }
    }
  });

  if (!country) {
    throw new Error('Country not found');
  }

  return country;
};

/**
 * Get states by country ID
 */
export const getStatesByCountryId = async (countryId: number, options?: {
  search?: string;
  limit?: number;
  offset?: number;
}) => {
  const { search, limit = 50, offset = 0 } = options || {};

  // Verify country exists
  const country = await db.country.findUnique({
    where: { countryId },
    select: { countryId: true, countryName: true }
  });

  if (!country) {
    throw new Error('Country not found');
  }

  const whereClause: Prisma.StateWhereInput = {
    countryId
  };

  if (search) {
    whereClause.stateName = { contains: search, mode: 'insensitive' };
  }

  const [states, total] = await Promise.all([
    db.state.findMany({
      where: whereClause,
      take: limit,
      skip: offset,
      orderBy: { stateName: 'asc' },
      include: {
        country: {
          select: { countryId: true, countryName: true, isoCode: true }
        }
      }
    }),
    db.state.count({ where: whereClause })
  ]);

  return {
    states,
    country,
    pagination: {
      total,
      limit,
      offset,
      hasMore: offset + limit < total
    }
  };
};

/**
 * Get cities by state ID
 */
export const getCitiesByStateId = async (stateId: number, options?: {
  search?: string;
  limit?: number;
  offset?: number;
}) => {
  const { search, limit = 100, offset = 0 } = options || {};

  // Verify state exists and get country info
  const state = await db.state.findUnique({
    where: { stateId },
    include: {
      country: {
        select: { countryId: true, countryName: true, isoCode: true }
      }
    }
  });

  if (!state) {
    throw new Error('State not found');
  }

  const whereClause: Prisma.CityWhereInput = {
    stateId
  };

  if (search) {
    whereClause.cityName = { contains: search, mode: 'insensitive' };
  }

  const [cities, total] = await Promise.all([
    db.city.findMany({
      where: whereClause,
      take: limit,
      skip: offset,
      orderBy: { cityName: 'asc' }
    }),
    db.city.count({ where: whereClause })
  ]);

  return {
    cities,
    state: {
      stateId: state.stateId,
      stateName: state.stateName,
      stateCode: state.stateCode
    },
    country: state.country,
    pagination: {
      total,
      limit,
      offset,
      hasMore: offset + limit < total
    }
  };
};

/**
 * Search across all location types
 */
export const searchLocations = async (query: string, options?: {
  types?: ('country' | 'state' | 'city')[];
  limit?: number;
}) => {
  const { types = ['country', 'state', 'city'], limit = 20 } = options || {};
  const results: LocationSearchResult[] = [];

  // Search countries
  if (types.includes('country')) {
    const countries = await db.country.findMany({
      where: {
        OR: [
          { countryName: { contains: query, mode: 'insensitive' } },
          { isoCode: { contains: query, mode: 'insensitive' } },
          { iso3Code: { contains: query, mode: 'insensitive' } }
        ]
      },
      take: Math.ceil(limit / types.length),
      orderBy: { countryName: 'asc' }
    });

    countries.forEach(country => {
      results.push({
        type: 'country',
        id: country.countryId,
        name: country.countryName,
        hierarchy: { country }
      });
    });
  }

  // Search states
  if (types.includes('state')) {
    const states = await db.state.findMany({
      where: {
        stateName: { contains: query, mode: 'insensitive' }
      },
      take: Math.ceil(limit / types.length),
      include: {
        country: true
      },
      orderBy: { stateName: 'asc' }
    });

    states.forEach(state => {
      results.push({
        type: 'state',
        id: state.stateId,
        name: state.stateName,
        hierarchy: {
          country: state.country,
          state
        }
      });
    });
  }

  // Search cities
  if (types.includes('city')) {
    const cities = await db.city.findMany({
      where: {
        cityName: { contains: query, mode: 'insensitive' }
      },
      take: Math.ceil(limit / types.length),
      include: {
        state: {
          include: {
            country: true
          }
        }
      },
      orderBy: { cityName: 'asc' }
    });

    cities.forEach(city => {
      results.push({
        type: 'city',
        id: city.cityId,
        name: city.cityName,
        hierarchy: {
          country: city.state.country,
          state: city.state,
          city
        }
      });
    });
  }

  return results.slice(0, limit);
};

/**
 * Get location hierarchy by city ID
 */
export const getLocationHierarchy = async (cityId: number) => {
  const city = await db.city.findUnique({
    where: { cityId },
    include: {
      state: {
        include: {
          country: true
        }
      }
    }
  });

  if (!city) {
    throw new Error('City not found');
  }

  return {
    country: city.state.country,
    state: city.state,
    city
  };
};

/**
 * Get regions (unique regions from countries)
 */
export const getRegions = async () => {
  const regions = await db.country.findMany({
    select: {
      region: true
    },
    where: {
      region: {
        not: null
      }
    },
    distinct: ['region'],
    orderBy: {
      region: 'asc'
    }
  });

  return regions.map(r => r.region).filter(Boolean);
};
