#!/usr/bin/env node

/**
 * Simple CLI script for seeding location data
 * Usage: node scripts/seed-locations.js [options]
 */

const { execSync } = require('child_process');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Helper function to print colored output
const print = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Helper function to run commands
const runCommand = (command, description) => {
  try {
    print(`\n${description}...`, 'cyan');
    const output = execSync(command, { 
      stdio: 'inherit', 
      cwd: path.join(__dirname, '..') 
    });
    return true;
  } catch (error) {
    print(`❌ Error: ${error.message}`, 'red');
    return false;
  }
};

// Main CLI function
const main = () => {
  const args = process.argv.slice(2);
  
  print('🌍 RiteFit.AI Location Data Seeding Tool', 'bright');
  print('=' .repeat(50), 'blue');
  
  // Parse command line arguments
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  if (args.includes('--stats')) {
    runCommand('npm run seed -- --stats', '📊 Getting seeding statistics');
    return;
  }
  
  if (args.includes('--reset')) {
    print('\n⚠️  WARNING: This will delete all existing location data!', 'yellow');
    print('Press Ctrl+C to cancel, or wait 5 seconds to continue...', 'yellow');
    
    // Give user time to cancel
    setTimeout(() => {
      runCommand('npm run seed -- --reset', '🗑️  Resetting location data');
    }, 5000);
    return;
  }
  
  if (args.includes('--countries')) {
    runCommand('npm run seed:countries', '🌍 Seeding countries');
    return;
  }
  
  if (args.includes('--states')) {
    runCommand('npm run seed:states', '🏛️ Seeding states');
    return;
  }
  
  if (args.includes('--cities')) {
    runCommand('npm run seed:cities', '🏙️ Seeding cities');
    return;
  }
  
  // Default: seed all location data
  const success = runCommand('npm run seed', '🚀 Seeding all location data');
  
  if (success) {
    print('\n🎉 Location data seeding completed successfully!', 'green');
    print('\nNext steps:', 'bright');
    print('• Check the data in your database', 'cyan');
    print('• Test the location APIs', 'cyan');
    print('• Run: node scripts/seed-locations.js --stats', 'cyan');
  } else {
    print('\n❌ Seeding failed. Please check the error messages above.', 'red');
    process.exit(1);
  }
};

// Show help information
const showHelp = () => {
  print('\nUsage: node scripts/seed-locations.js [options]\n', 'bright');
  
  print('Options:', 'bright');
  print('  --help, -h     Show this help message', 'cyan');
  print('  --stats        Show current seeding statistics', 'cyan');
  print('  --reset        Reset all location data (WARNING: destructive)', 'yellow');
  print('  --countries    Seed only countries', 'cyan');
  print('  --states       Seed only states', 'cyan');
  print('  --cities       Seed only cities', 'cyan');
  print('  (no options)   Seed all location data', 'green');
  
  print('\nExamples:', 'bright');
  print('  node scripts/seed-locations.js                 # Seed all data', 'cyan');
  print('  node scripts/seed-locations.js --countries     # Seed only countries', 'cyan');
  print('  node scripts/seed-locations.js --stats         # Show statistics', 'cyan');
  print('  node scripts/seed-locations.js --reset         # Reset all data', 'yellow');
  
  print('\nNotes:', 'bright');
  print('• Countries must be seeded before states', 'yellow');
  print('• States must be seeded before cities', 'yellow');
  print('• The seeding process is idempotent (safe to run multiple times)', 'green');
  print('• Use --reset with caution as it will delete all existing data', 'red');
};

// Run the CLI
main();
