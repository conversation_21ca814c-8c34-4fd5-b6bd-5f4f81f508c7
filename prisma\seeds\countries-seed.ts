import { 
  prisma, 
  readJsonFile, 
  logProgress, 
  processBatch, 
  transformCountryData,
  CountryRawData 
} from './seed-utils';

/**
 * Seed countries data
 */
export const seedCountries = async (): Promise<void> => {
  try {
    logProgress('🌍 Starting countries seeding...');
    
    // Check if countries already exist
    const existingCount = await prisma.countries.count();
    if (existingCount > 0) {
      logProgress(`⚠️  Found ${existingCount} existing countries. Skipping countries seeding.`);
      return;
    }
    
    // Read countries data
    logProgress('📖 Reading countries data...');
    const rawCountries = readJsonFile<CountryRawData>('countries.json');
    logProgress(`📊 Found ${rawCountries.length} countries to process`);
    
    // Transform and validate data
    logProgress('🔄 Transforming countries data...');
    const transformedCountries = rawCountries.map((country, index) => {
      try {
        return transformCountryData(country, index);
      } catch (error) {
        console.error(`Error transforming country ${country.name}:`, error);
        throw error;
      }
    });
    
    // Batch insert countries
    const BATCH_SIZE = 50;
    logProgress(`💾 Inserting countries in batches of ${BATCH_SIZE}...`);
    
    await processBatch(
      transformedCountries,
      BATCH_SIZE,
      async (batch) => {
        return await prisma.$transaction(async (tx) => {
          const results = [];
          for (const country of batch) {
            try {
              const result = await tx.countries.create({
                data: country
              });
              results.push(result);
            } catch (error) {
              console.error(`Error inserting country ${country.name}:`, error);
              // Continue with other countries in the batch
              continue;
            }
          }
          return results;
        });
      },
      'Inserting countries'
    );
    
    // Verify insertion
    const finalCount = await prisma.countries.count();
    logProgress(`✅ Countries seeding completed! Inserted ${finalCount} countries.`);
    
  } catch (error) {
    console.error('❌ Error seeding countries:', error);
    throw error;
  }
};

