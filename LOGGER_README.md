# Simple Logger Utility Documentation

## Overview

This is a lightweight, simple logger utility for the RiteFit.AI backend application. It provides colored console logging for API requests, responses, and general application logging without external dependencies.

## Features

- ✅ **API Request Logging** - Log request ID, URL, data, method, and IP
- ✅ **Response Success Logging** - Log request ID, data, and status code
- ✅ **Response Error Logging** - Log request ID, error message, and stack trace
- ✅ **Simple Info/Error/Warning Logging** - Basic application logging
- ✅ **Unique Request IDs** - Each request gets a unique identifier for tracking
- ✅ **Timestamps** - All logs include ISO timestamps
- ✅ **Colored Console Output** - Different colors for different log types
- ✅ **No External Dependencies** - Pure Node.js implementation

## Installation

No additional dependencies required! The logger is built using only Node.js built-in modules.

## Usage

### Basic Logging

```typescript
import { logger } from '@/utils/logger';

// Simple info logging
logger.info('User logged in successfully');
logger.info('Processing data', { userId: 123, action: 'login' });

// Warning logging
logger.warning('Rate limit approaching');
logger.warning('Deprecated endpoint used', { endpoint: '/old-api' });

// Error logging
logger.error('Database connection failed');
logger.error('Authentication failed', new Error('Invalid credentials'));
```

### API Request Logging

The logger automatically captures API requests when the middleware is enabled:

```typescript
// Automatic request logging for all API calls
// Example: GET /api/users with body data
// Output: [2024-01-15T10:30:25.123Z] REQUEST [req_1705312225123_abc123] GET /api/users | IP: ***********
```

### API Response Logging

Responses are automatically logged based on status codes:

```typescript
// Success response (status 200-399)
// Output: [2024-01-15T10:30:25.456Z] RESPONSE_SUCCESS [req_1705312225123_abc123] Status: 200

// Error response (status 400+)
// Output: [2024-01-15T10:30:25.789Z] RESPONSE_ERROR [req_1705312225123_abc123] Error: Validation failed
```

### Manual API Logging

You can also manually log API requests and responses:

```typescript
import { logger, generateRequestId } from '@/utils/logger';

const requestId = generateRequestId();

// Log request
logger.logRequest(requestId, '/api/users', { name: 'John' }, 'POST', '***********');

// Log success response
logger.logResponseSuccess(requestId, { id: 123, name: 'John' }, 201);

// Log error response
logger.logResponseError(requestId, 'User already exists', 'Error stack trace...');
```

## API Endpoints for Testing

The logger includes example endpoints for testing:

### Basic Logging Example
```
GET /api/examples/info
```

### Detailed Logging Example
```
GET /api/examples/detailed?detailed_logging=true
```

### Error Logging Example
```
GET /api/examples/error
```

### Warning Logging Example
```
GET /api/examples/warning
```

### POST Request Example
```
POST /api/examples/post-example
Content-Type: application/json

{
  "name": "Test User",
  "email": "<EMAIL>"
}
```

## Log Files

Logs are automatically written to the `logs/` directory:

- `combined-YYYY-MM-DD.log` - All logs
- `error-YYYY-MM-DD.log` - Error logs only
- `api-requests-YYYY-MM-DD.log` - API request/response logs

## Log Formats

### Console Output (Development)
```
14:30:25 info [req_1234567890_abc123]: User logged in successfully {"userId": 123}
```

### File Output (Production)
```json
{
  "timestamp": "2024-01-15 14:30:25.123",
  "level": "info",
  "message": "User logged in successfully",
  "requestId": "req_1234567890_abc123",
  "userId": 123
}
```

## Security Features

### Automatic Data Redaction

Sensitive fields are automatically redacted from logs:

**Headers:** `authorization`, `cookie`, `x-api-key`
**Body Fields:** `password`, `token`, `secret`

```typescript
// Original request
{
  "email": "<EMAIL>",
  "password": "secretpassword"
}

// Logged as
{
  "email": "<EMAIL>",
  "password": "[REDACTED]"
}
```

## Middleware Integration

The logger middleware is automatically applied to all routes:

```typescript
// In app.ts
app.use(requestLogger); // Applied early in middleware chain
```

## Error Handling Integration

Errors are automatically logged with full context:

```typescript
// Global error handler logs all unhandled errors
app.use(globalErrorHandler);

// 404 errors are also logged
app.all('*', notFoundHandler);
```

## Configuration Options

### Request Logging Configuration

```typescript
interface RequestLogConfig {
  enableDetailedLogging?: boolean;  // Enable detailed logging
  logHeaders?: boolean;             // Log request headers
  logBody?: boolean;                // Log request body
  logQuery?: boolean;               // Log query parameters
  logParams?: boolean;              // Log route parameters
  excludeHeaders?: string[];        // Headers to redact
  excludeBodyFields?: string[];     // Body fields to redact
}
```

### Logger Configuration

```typescript
interface LoggerConfig {
  level: LogLevel;                  // Minimum log level
  enableFileLogging: boolean;       // Enable file output
  enableConsoleLogging: boolean;    // Enable console output
  logDirectory: string;             // Log file directory
  maxFileSize: string;              // Max file size before rotation
  maxFiles: string;                 // Number of files to keep
  datePattern: string;              // Date pattern for file names
}
```

## Best Practices

1. **Use Request IDs**: Always pass the request ID to logger methods in controllers
2. **Appropriate Log Levels**: Use appropriate log levels (debug for development, info for important events, warn for issues, error for failures)
3. **Structured Metadata**: Include relevant metadata objects for better searchability
4. **Sensitive Data**: Never log sensitive information directly
5. **Performance**: Use debug level for verbose logging that might impact performance

## Troubleshooting

### Common Issues

1. **Logs not appearing**: Check LOG_LEVEL environment variable
2. **File permission errors**: Ensure the application has write access to the logs directory
3. **Large log files**: Log rotation is automatic, but check disk space regularly
