# 🌍 Location API Documentation

A comprehensive location API providing hierarchical geographical data including countries, states, and cities with detailed information such as ISO codes, currencies, timezones, and coordinates.

## 📋 Table of Contents

- [Features](#features)
- [Database Schema](#database-schema)
- [API Endpoints](#api-endpoints)
- [Data Structure](#data-structure)
- [Usage Examples](#usage-examples)
- [Seeding Data](#seeding-data)

## ✨ Features

- **Hierarchical Data**: Countries → States → Cities
- **Rich Metadata**: ISO codes, currencies, timezones, coordinates
- **Search Functionality**: Search across all location types
- **Pagination**: Efficient data retrieval for large datasets
- **Type Safety**: Full TypeScript support with DTOs
- **Performance**: Optimized queries with proper indexing

## 🗄️ Database Schema

### Country Model
```prisma
model Country {
  countryId         Int        @id @default(autoincrement())
  countryName       String     @db.VarChar(100)
  isoCode           String     @unique @db.VarChar(2)     // ISO 3166-1 alpha-2
  iso3Code          String     @unique @db.VarChar(3)     // ISO 3166-1 alpha-3
  numericCode       String?    @db.VarChar(3)             // ISO 3166-1 numeric
  phoneCode         String     @db.VarChar(10)            // Phone code
  capital           String?    @db.VarChar(100)           // Capital city
  currency          String?    @db.VarChar(10)            // Currency code (USD, INR)
  currencyName      String?    @db.VarChar(100)           // Currency name
  currencySymbol    String?    @db.VarChar(10)            // Currency symbol
  tld               String?    @db.VarChar(10)            // Top level domain
  native            String?    @db.VarChar(100)           // Native name
  region            String?    @db.VarChar(50)            // Region (Asia, Europe)
  subregion         String?    @db.VarChar(50)            // Subregion
  latitude          Decimal?   @db.Decimal(10, 8)         // Latitude
  longitude         Decimal?   @db.Decimal(11, 8)         // Longitude
  emoji             String?    @db.VarChar(10)            // Flag emoji
  emojiU            String?    @db.VarChar(20)            // Unicode for emoji
  timezones         Json?                                 // Timezone objects array
  translations      Json?                                 // Name translations
  // ... audit fields
}
```

### State Model
```prisma
model State {
  stateId    Int        @id @default(autoincrement())
  stateName  String     @db.VarChar(100)
  stateCode  String?    @db.VarChar(10)            // State/Province code
  countryId  Int                                   // Foreign key to Country
  type       String?    @db.VarChar(50)            // State, Province, Territory
  latitude   Decimal?   @db.Decimal(10, 8)         // Latitude
  longitude  Decimal?   @db.Decimal(11, 8)         // Longitude
  // ... audit fields
}
```

### City Model
```prisma
model City {
  cityId     Int       @id @default(autoincrement())
  cityName   String    @db.VarChar(100)
  stateId    Int                                   // Foreign key to State
  latitude   Decimal?  @db.Decimal(10, 8)          // Latitude
  longitude  Decimal?  @db.Decimal(11, 8)          // Longitude
  // ... audit fields
}
```

## 🚀 API Endpoints

### Countries

#### Get All Countries
```http
GET /api/locations/countries
```

**Query Parameters:**
- `search` (optional): Search by name, ISO code, or ISO3 code
- `region` (optional): Filter by region (Asia, Europe, etc.)
- `limit` (optional): Number of results per page (default: 50)
- `offset` (optional): Number of results to skip (default: 0)

**Example:**
```http
GET /api/locations/countries?search=india&limit=10
GET /api/locations/countries?region=Asia&offset=20
```

#### Get Country by ID
```http
GET /api/locations/countries/:id
```

**Example:**
```http
GET /api/locations/countries/1
```

#### Get States by Country
```http
GET /api/locations/countries/:id/states
```

**Query Parameters:**
- `search` (optional): Search by state name
- `limit` (optional): Results per page (default: 50)
- `offset` (optional): Results to skip (default: 0)

### States

#### Get Cities by State
```http
GET /api/locations/states/:id/cities
```

**Query Parameters:**
- `search` (optional): Search by city name
- `limit` (optional): Results per page (default: 100)
- `offset` (optional): Results to skip (default: 0)

### Search

#### Search Locations
```http
GET /api/locations/search
```

**Query Parameters:**
- `q` (required): Search query
- `types` (optional): Comma-separated types (country,state,city)
- `limit` (optional): Maximum results (default: 20)

**Example:**
```http
GET /api/locations/search?q=new&types=city,state&limit=10
```

### Utilities

#### Get Location Hierarchy
```http
GET /api/locations/cities/:id/hierarchy
```

Returns complete hierarchy: City → State → Country

#### Get Regions
```http
GET /api/locations/regions
```

Returns all unique regions from countries.

## 📊 Data Structure

### Timezone Object
```json
{
  "zoneName": "Asia/Kolkata",
  "gmtOffset": 19800,
  "gmtOffsetName": "UTC+05:30",
  "abbreviation": "IST",
  "tzName": "Indian Standard Time"
}
```

### Translation Object
```json
{
  "hi": "भारत",
  "fr": "Inde",
  "es": "India",
  "de": "Indien"
}
```

### Sample Country Response
```json
{
  "success": true,
  "message": "Country details retrieved successfully",
  "data": {
    "countryId": 1,
    "countryName": "India",
    "isoCode": "IN",
    "iso3Code": "IND",
    "numericCode": "356",
    "phoneCode": "+91",
    "capital": "New Delhi",
    "currency": "INR",
    "currencyName": "Indian Rupee",
    "currencySymbol": "₹",
    "tld": ".in",
    "native": "भारत",
    "region": "Asia",
    "subregion": "Southern Asia",
    "latitude": 20.00000000,
    "longitude": 77.00000000,
    "emoji": "🇮🇳",
    "emojiU": "U+1F1EE U+1F1F3",
    "timezones": [
      {
        "zoneName": "Asia/Kolkata",
        "gmtOffset": 19800,
        "gmtOffsetName": "UTC+05:30",
        "abbreviation": "IST",
        "tzName": "Indian Standard Time"
      }
    ],
    "translations": {
      "hi": "भारत",
      "fr": "Inde",
      "es": "India"
    }
  }
}
```

## 🌱 Seeding Data

Run the location data seeding script:

```bash
# Generate Prisma client first
npm run prisma:generate

# Run the seeding script
npx ts-node prisma/seeds/location-seed.ts
```

The seeding script includes:
- 3 sample countries (India, USA, UK) with full metadata
- Multiple states/provinces for each country
- Major cities with coordinates
- Complete timezone and translation data

## 🔧 Usage Examples

### Frontend Integration

```typescript
// Get countries for a dropdown
const countries = await fetch('/api/locations/countries?limit=250');

// Get states when country is selected
const states = await fetch(`/api/locations/countries/${countryId}/states`);

// Get cities when state is selected
const cities = await fetch(`/api/locations/states/${stateId}/cities`);

// Search for locations
const results = await fetch('/api/locations/search?q=mumbai');

// Get complete location hierarchy
const hierarchy = await fetch(`/api/locations/cities/${cityId}/hierarchy`);
```

### Backend Integration

```typescript
import * as locationService from '@/modules/support/location/location.service';

// Use in your services
const countries = await locationService.getAllCountries({ region: 'Asia' });
const hierarchy = await locationService.getLocationHierarchy(cityId);
```

## 🎯 Best Practices

1. **Caching**: Implement Redis caching for frequently accessed data
2. **Indexing**: Add database indexes on search fields
3. **Validation**: Always validate location IDs in your business logic
4. **Pagination**: Use pagination for large datasets
5. **Error Handling**: Handle location not found scenarios gracefully

## 📈 Performance Tips

- Use `limit` and `offset` for pagination
- Cache country and state data (changes infrequently)
- Use search with minimum 2-3 characters
- Consider implementing autocomplete with debouncing
