# 🌍 Location Data Seeding Guide

This guide provides comprehensive instructions for seeding your database with countries, states, and cities data using the RiteFit.AI backend seeding system.

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Available Methods](#available-methods)
- [Data Overview](#data-overview)
- [Command Line Usage](#command-line-usage)
- [API Endpoints](#api-endpoints)
- [Programmatic Usage](#programmatic-usage)
- [Troubleshooting](#troubleshooting)
- [Advanced Configuration](#advanced-configuration)

## 🚀 Quick Start

### Method 1: Using NPM Scripts (Recommended)
```bash
# Seed all location data
npm run seed

# Or use the interactive CLI
npm run seed:cli
```

### Method 2: Using the CLI Script
```bash
# Seed all data
node scripts/seed-locations.js

# Show help
node scripts/seed-locations.js --help

# Check statistics
node scripts/seed-locations.js --stats
```

### Method 3: Using API Endpoints
```bash
# Seed all location data
curl -X POST http://localhost:3000/api/seed/location

# Check statistics
curl http://localhost:3000/api/seed/stats
```

## 📊 Data Overview

The seeding system populates three main tables:

### Countries (~250 records)
- **ISO Codes**: 2-letter, 3-letter, and numeric codes
- **Geographic Data**: Latitude, longitude coordinates
- **Currency Info**: Currency codes, symbols, and names
- **Timezone Data**: Complete timezone information as JSON
- **Phone Codes**: International dialing codes

### States (~5,000+ records)
- **Geographic Data**: Latitude, longitude coordinates
- **State Codes**: Official state/province codes
- **Country Relations**: Linked to parent countries
- **Language Info**: Primary languages (where available)

### Cities (~35,000+ records)
- **Geographic Data**: Latitude, longitude coordinates
- **State Relations**: Linked to parent states
- **Population Centers**: Major cities worldwide

## 🛠️ Available Methods

### 1. Command Line Scripts

#### NPM Scripts
```bash
npm run seed                    # Seed all location data
npm run seed:countries         # Seed only countries
npm run seed:states           # Seed only states
npm run seed:cities           # Seed only cities
npm run seed:cli              # Interactive CLI tool
```

#### Direct CLI Usage
```bash
node scripts/seed-locations.js                 # Seed all data
node scripts/seed-locations.js --countries     # Countries only
node scripts/seed-locations.js --states        # States only
node scripts/seed-locations.js --cities        # Cities only
node scripts/seed-locations.js --stats         # Show statistics
node scripts/seed-locations.js --reset         # Reset all data
node scripts/seed-locations.js --help          # Show help
```

### 2. API Endpoints

All endpoints are available under `/api/seed/`:

```http
POST   /api/seed/location              # Seed all location data
POST   /api/seed/countries             # Seed only countries
POST   /api/seed/states                # Seed only states
POST   /api/seed/cities                # Seed only cities
POST   /api/seed/location/if-not-exists # Seed only if not already seeded
GET    /api/seed/stats                 # Get seeding statistics
DELETE /api/seed/location              # Reset all location data
```

#### Example API Usage
```javascript
// Seed all location data
const response = await fetch('/api/seed/location', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' }
});

// Check if data is already seeded
const stats = await fetch('/api/seed/stats').then(r => r.json());
console.log(stats.data); // { countries: 250, states: 5000, cities: 35000, isSeeded: true }
```

### 3. Programmatic Usage

#### Using the SeedService
```typescript
import { SeedService } from '@/services/seed-service';

// Seed all location data
const result = await SeedService.seedAllLocationData();
if (result.success) {
  console.log('Seeded:', result.stats);
}

// Check if already seeded
const isSeeded = await SeedService.isLocationDataSeeded();

// Seed only if not exists
const conditionalResult = await SeedService.seedIfNotExists();
```

#### Using Direct Seed Functions
```typescript
import seedModule from '@/prisma/seeds/index';

// Seed all data
await seedModule.seedLocationData();

// Get statistics
const stats = await seedModule.getSeedingStats();

// Reset data
await seedModule.resetLocationData();
```

## 🔧 Command Line Usage

### Interactive CLI Tool
```bash
npm run seed:cli
```

The CLI provides a user-friendly interface with:
- Colored output for better readability
- Progress tracking
- Error handling
- Safety confirmations for destructive operations

### Advanced CLI Options
```bash
# Show detailed statistics
node scripts/seed-locations.js --stats

# Reset with confirmation
node scripts/seed-locations.js --reset

# Seed specific components
node scripts/seed-locations.js --countries
node scripts/seed-locations.js --states
node scripts/seed-locations.js --cities
```

## 🌐 API Endpoints

### Seed All Location Data
```http
POST /api/seed/location
```
**Response:**
```json
{
  "success": true,
  "message": "Location data seeded successfully",
  "data": {
    "stats": {
      "countries": 250,
      "states": 5000,
      "cities": 35000
    }
  }
}
```

### Get Seeding Statistics
```http
GET /api/seed/stats
```
**Response:**
```json
{
  "success": true,
  "message": "Seeding statistics retrieved successfully",
  "data": {
    "countries": 250,
    "states": 5000,
    "cities": 35000,
    "isSeeded": true
  }
}
```

### Conditional Seeding
```http
POST /api/seed/location/if-not-exists
```
**Response:**
```json
{
  "success": true,
  "message": "Location data already exists",
  "data": {
    "wasAlreadySeeded": true,
    "stats": {
      "countries": 250,
      "states": 5000,
      "cities": 35000
    }
  }
}
```

## 💻 Programmatic Usage

### Application Startup Seeding
```typescript
// In your app.ts or server.ts
import { SeedService } from '@/services/seed-service';

const initializeApp = async () => {
  // Seed location data if not already present
  const seedResult = await SeedService.seedIfNotExists();
  
  if (seedResult.success && !seedResult.wasAlreadySeeded) {
    console.log('✅ Location data seeded successfully');
  }
  
  // Start your server
  app.listen(PORT);
};
```

### Background Job Seeding
```typescript
import { SeedService } from '@/services/seed-service';

const backgroundSeedJob = async () => {
  try {
    const isSeeded = await SeedService.isLocationDataSeeded();
    
    if (!isSeeded) {
      console.log('🌍 Starting background location data seeding...');
      await SeedService.seedAllLocationData();
      console.log('✅ Background seeding completed');
    }
  } catch (error) {
    console.error('❌ Background seeding failed:', error);
  }
};
```

## 🚨 Troubleshooting

### Common Issues

1. **"No countries found" Error**
   ```bash
   # Solution: Seed countries first
   npm run seed:countries
   ```

2. **"No states found" Error**
   ```bash
   # Solution: Seed states after countries
   npm run seed:states
   ```

3. **Memory Issues**
   ```bash
   # Solution: Increase Node.js memory limit
   node --max-old-space-size=4096 scripts/seed-locations.js
   ```

4. **Database Connection Issues**
   - Verify `DATABASE_URL` in `.env` file
   - Ensure MySQL server is running
   - Check database permissions

### Debugging

#### Enable Detailed Logging
```bash
# Set environment variable for detailed logs
DEBUG=seed:* npm run seed
```

#### Check Database State
```sql
-- Verify data integrity
SELECT 
  (SELECT COUNT(*) FROM countries) as countries,
  (SELECT COUNT(*) FROM states) as states,
  (SELECT COUNT(*) FROM cities) as cities;

-- Check relationships
SELECT c.name, COUNT(s.stateId) as states_count
FROM countries c
LEFT JOIN states s ON c.countryId = s.countryId
GROUP BY c.countryId
ORDER BY states_count DESC
LIMIT 10;
```

## ⚙️ Advanced Configuration

### Environment Variables
```env
# Required
DATABASE_URL="mysql://user:password@localhost:3306/database"

# Optional
SYSTEM_USER_ID=1                    # User ID for audit fields
DEBUG=seed:*                        # Enable debug logging
NODE_ENV=development                # Environment mode
```

### Batch Size Optimization
Edit batch sizes in seed files for performance tuning:
```typescript
// In prisma/seeds/countries-seed.ts
const BATCH_SIZE = 50;  // Adjust based on your system

// In prisma/seeds/states-seed.ts
const BATCH_SIZE = 100; // Increase for better performance

// In prisma/seeds/cities-seed.ts
const BATCH_SIZE = 200; // Decrease if memory issues occur
```

### Custom Data Sources
To use custom data files:
1. Replace JSON files in `prisma/data/`
2. Ensure data follows the expected format
3. Run seeding scripts

## 📝 Notes

- **Idempotent**: Safe to run multiple times without creating duplicates
- **Transactional**: Each batch is wrapped in database transactions
- **Hierarchical**: Respects foreign key constraints (Countries → States → Cities)
- **Performant**: Optimized batch processing for large datasets
- **Resilient**: Comprehensive error handling and recovery
