import { Request, Response } from 'express';
import { SeedService } from '../../../services/seed-service';
import { sendSuccessResponse, sendErrorResponse } from '../../../utils/response-util';

/**
 * Seed all location data (countries, states, cities)
 */
export const seedAllLocationData = async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await SeedService.seedAllLocationData();
    
    if (result.success) {
      sendSuccessResponse(res, {
        message: result.message,
        data: {
          stats: result.stats
        }
      });
    } else {
      sendErrorResponse(res, result.message, 500, result.error);
    }
  } catch (error) {
    console.error('Error in seedAllLocationData controller:', error);
    sendErrorResponse(res, 'Internal server error', 500);
  }
};

/**
 * Seed only countries
 */
export const seedCountries = async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await SeedService.seedCountries();
    
    if (result.success) {
      sendSuccessResponse(res, {
        message: result.message,
        data: {
          count: result.count
        }
      });
    } else {
      sendErrorResponse(res, result.message, 500, result.error);
    }
  } catch (error) {
    console.error('Error in seedCountries controller:', error);
    sendErrorResponse(res, 'Internal server error', 500);
  }
};

/**
 * Seed only states
 */
export const seedStates = async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await SeedService.seedStates();
    
    if (result.success) {
      sendSuccessResponse(res, {
        message: result.message,
        data: {
          count: result.count
        }
      });
    } else {
      sendErrorResponse(res, result.message, 500, result.error);
    }
  } catch (error) {
    console.error('Error in seedStates controller:', error);
    sendErrorResponse(res, 'Internal server error', 500);
  }
};

/**
 * Seed only cities
 */
export const seedCities = async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await SeedService.seedCities();
    
    if (result.success) {
      sendSuccessResponse(res, {
        message: result.message,
        data: {
          count: result.count
        }
      });
    } else {
      sendErrorResponse(res, result.message, 500, result.error);
    }
  } catch (error) {
    console.error('Error in seedCities controller:', error);
    sendErrorResponse(res, 'Internal server error', 500);
  }
};

/**
 * Get seeding statistics
 */
export const getSeedingStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const stats = await SeedService.getSeedingStats();
    
    sendSuccessResponse(res, {
      message: 'Seeding statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    console.error('Error in getSeedingStats controller:', error);
    sendErrorResponse(res, 'Internal server error', 500);
  }
};

/**
 * Reset all location data
 */
export const resetLocationData = async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await SeedService.resetLocationData();
    
    if (result.success) {
      sendSuccessResponse(res, {
        message: result.message
      });
    } else {
      sendErrorResponse(res, result.message, 500, result.error);
    }
  } catch (error) {
    console.error('Error in resetLocationData controller:', error);
    sendErrorResponse(res, 'Internal server error', 500);
  }
};

/**
 * Seed location data if not already seeded
 */
export const seedIfNotExists = async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await SeedService.seedIfNotExists();
    
    if (result.success) {
      sendSuccessResponse(res, {
        message: result.message,
        data: {
          wasAlreadySeeded: result.wasAlreadySeeded,
          stats: result.stats
        }
      });
    } else {
      sendErrorResponse(res, result.message, 500, result.error);
    }
  } catch (error) {
    console.error('Error in seedIfNotExists controller:', error);
    sendErrorResponse(res, 'Internal server error', 500);
  }
};
