import express, { Application } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import config from '@/config/config';
import { globalErrorHandler, notFoundHandler } from '@/middlewares/error-middleware';
import { requestLoggingMiddleware } from '@/middlewares/request-middleware';
import { setupRoutes } from '@/router';

// Create and configure Express application
export const createExpressApp = (): Application => {
  const app = express();

  // Basic middleware
  app.use(helmet());
  app.use(cors({ origin: config.CORS_ORIGIN }));
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // Request logging middleware (should be early in the chain)
  app.use(requestLoggingMiddleware);


  // Setup routes
  setupRoutes(app);

  // Error handling
  app.all('*', notFoundHandler);
  app.use(globalErrorHandler);

  return app;
};

export default createExpressApp;
