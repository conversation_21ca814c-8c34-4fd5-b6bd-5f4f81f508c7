// This is your Prisma schema file
// Learn more: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

enum Status {
  active
  inactive
  deleted
}

model Cities {
  cityId     Int        @id @default(autoincrement()) @map("city_id")
  name       String     @db.VarChar(100)
  stateId    Int        @map("state_id")
  latitude   Decimal?   @db.Decimal(10, 8)
  longitude  Decimal?   @db.Decimal(11, 8)
  createdAt  DateTime   @default(now()) @map("created_at")
  createdBy  Int       @map("created_by")
  updatedAt  DateTime   @updatedAt @map("updated_at")
  updatedBy  Int       @map("updated_by")
  status     Status     @default(active)

  // Relations
  state      States     @relation(fields: [stateId], references: [stateId])

  @@map("cities")
}

model States {
  stateId     Int        @id @default(autoincrement()) @map("state_id")
  name        String     @db.VarChar(100)
  stateCode   String     @map("state_code") @db.VarChar(10)
  countryId   Int        @map("country_id")
  language    String?      @db.VarChar(100)
  latitude    Decimal?   @db.Decimal(10, 8)
  longitude   Decimal?   @db.Decimal(11, 8)
  createdAt   DateTime   @default(now()) @map("created_at")
  createdBy   Int        @map("created_by")
  updatedAt   DateTime   @updatedAt @map("updated_at")
  updatedBy   Int        @map("updated_by")
  status      Status     @default(active)   

  // Relations
  country     Countries  @relation(fields: [countryId], references: [countryId])
  cities      Cities[]

  @@map("states")
}

model Countries {
  countryId       Int      @id @default(autoincrement()) @map("country_id")
  isoCode2        String   @unique @map("iso_code") @db.VarChar(2)
  isoCode3        String   @unique @map("iso_code_3") @db.VarChar(3)
  numericCode     String?  @unique @map("numeric_code") @db.VarChar(3)
  name            String   @db.VarChar(100)
  phonecode       String   @db.VarChar(10)
  flag            String?  @db.VarChar(10)
  currency        String?  @db.VarChar(10)
  currencySymbol  String?  @db.VarChar(10)
  currencyName    String?  @db.VarChar(100)
  latitude        Decimal? @db.Decimal(10, 8)
  longitude       Decimal? @db.Decimal(11, 8)
  timeZones       Json?    @db.Json
  createdAt       DateTime @default(now()) @map("created_at")
  createdBy       Int      @map("created_by")
  updatedAt       DateTime @updatedAt @map("updated_at")
  updatedBy       Int      @map("updated_by")
  status          Status   @default(active)

  // Relations
  states          States[]

  @@map("countries")
}
