import { PrismaClient } from '@prisma/client';
import config from './config';

// Prisma Client instance
let prismaInstance: PrismaClient | null = null;

// Create Prisma client instance
export const createPrismaClient = (): PrismaClient => {
  if (!prismaInstance) {
    prismaInstance = new PrismaClient({
      log: config.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
      errorFormat: 'pretty',
    });
  }
  return prismaInstance;
};

// Get Prisma client instance
export const getPrismaClient = (): PrismaClient => {
  if (!prismaInstance) {
    return createPrismaClient();
  }
  return prismaInstance;
};

// Connect to database
export const connectDatabase = async (): Promise<void> => {
  try {
    const prisma = getPrismaClient();
    await prisma.$connect();
    console.log('✅ Database connected successfully');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  }
};

// Disconnect from database
export const disconnectDatabase = async (): Promise<void> => {
  try {
    const prisma = getPrismaClient();
    await prisma.$disconnect();
    console.log('✅ Database disconnected successfully');
  } catch (error) {
    console.error('❌ Database disconnection failed:', error);
  }
};

// Database health check
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    const prisma = getPrismaClient();
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('❌ Database health check failed:', error);
    return false;
  }
};

// Export the prisma instance for direct use
export const db = getPrismaClient();
export default db;
