{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/config/*": ["src/config/*"], "@/utils/*": ["src/utils/*"], "@/middlewares/*": ["src/middlewares/*"], "@/services/*": ["src/services/*"], "@/routes/*": ["src/routes/*"], "@/controllers/*": ["src/controllers/*"], "@/router/*": ["src/router/*"], "@/modules/*": ["src/modules/*"]}, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"], "ts-node": {"require": ["tsconfig-paths/register"]}}