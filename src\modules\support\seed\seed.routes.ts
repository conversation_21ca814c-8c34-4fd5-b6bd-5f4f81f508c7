import { Router } from 'express';
import {
  seedAllLocationData,
  seedCountries,
  seedStates,
  seedCities,
  getSeedingStats,
  resetLocationData,
  seedIfNotExists
} from './seed.controller';

const router = Router();

/**
 * @route   POST /api/seed/location
 * @desc    Seed all location data (countries, states, cities)
 * @access  Admin only (should be protected in production)
 */
router.post('/location', seedAllLocationData);

/**
 * @route   POST /api/seed/countries
 * @desc    Seed only countries
 * @access  Admin only (should be protected in production)
 */
router.post('/countries', seedCountries);

/**
 * @route   POST /api/seed/states
 * @desc    Seed only states
 * @access  Admin only (should be protected in production)
 */
router.post('/states', seedStates);

/**
 * @route   POST /api/seed/cities
 * @desc    Seed only cities
 * @access  Admin only (should be protected in production)
 */
router.post('/cities', seedCities);

/**
 * @route   GET /api/seed/stats
 * @desc    Get seeding statistics
 * @access  Admin only (should be protected in production)
 */
router.get('/stats', getSeedingStats);

/**
 * @route   DELETE /api/seed/location
 * @desc    Reset all location data
 * @access  Admin only (should be protected in production)
 */
router.delete('/location', resetLocationData);

/**
 * @route   POST /api/seed/location/if-not-exists
 * @desc    Seed location data only if not already seeded
 * @access  Admin only (should be protected in production)
 */
router.post('/location/if-not-exists', seedIfNotExists);

export default router;
